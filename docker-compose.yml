services:
  fastbot:
      image: phurhard/fastbot
      build:
        context: ./microservices/fastbot
        dockerfile: Dockerfile
      env_file:
        - ./microservices/fastbot/.env
      container_name: fastbot_service
      platform: linux/amd64
      restart: always
      ports:
        - "8001:8001"

      volumes:
        - ./logs/fast/:/var/log/
      develop:
        watch:
          - action: sync+restart
            path: ./app
            target: /app/app
          - action: rebuild
            path: ./requirements.txt
            target: /app/requirements.txt
      healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
        interval: 300s
        timeout: 10s
        retries: 5
      depends_on:
        redis:
          condition: service_healthy
      networks:
        - frontend
        - backend

  authentication_service:
    image: phurhard/authentication_service
    build:
      context: ./microservices/authentication_service
      dockerfile: Dockerfile
    env_file:
      - ./microservices/authentication_service/.env
    container_name: authentication_service
    restart: always
    ports:
      - "7777:7777"
    volumes:
      - ./logs/auth/:/app/app/logs/
    develop:
      watch:
        - action: sync+restart
          path: ./app
          target: /app/app
        - action: rebuild
          path: ./requirements.txt
          target: /app/requirements.txt
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7777/auth_status"]
      interval: 300s
      timeout: 30s
      retries: 5
      start_period: 60s
    networks:
      - frontend

  settings_and_payment:
    image: phurhard/settings_and_payment
    build:
      context: ./microservices/settings_and_payment
      dockerfile: Dockerfile
    env_file:
      - ./microservices/settings_and_payment/.env
    container_name: settings_and_payment
    platform: linux/amd64
    restart: always
    ports:
      - "8005:8005"
    volumes:
      - ./logs/settings/:/app/app/logs/
    develop:
      watch:
        - action: sync+restart
          path: ./app
          target: /app/app
        - action: rebuild
          path: ./requirements.txt
          target: /app/requirements.txt
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/settings_status"]
      interval: 300s
      timeout: 10s
      retries: 5
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - frontend
      - backend

  socials_service:
    image: phurhard/socials_service
    build:
      context: ./microservices/socials_service
      dockerfile: Dockerfile
    env_file:
      - ./microservices/socials_service/.env
    container_name: socials_service
    restart: always
    ports:
      - "8006:8006"
    volumes:
      - ./logs/social/:/app/app/logs/
    develop:
      watch:
        - action: sync+restart
          path: ./app
          target: /app/app
        - action: rebuild
          path: ./requirements.txt
          target: /app/requirements.txt
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8006/socials_status"]
      interval: 300s
      timeout: 30s
      retries: 5
      start_period: 60s
    networks:
      - frontend

  redis:
    image: redis
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 300s
      timeout: 10s
      retries: 5
    networks:
      - backend

  qdrant:
    image: qdrant/qdrant
    container_name: qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ./qdrant_storage:/qdrant/storage
    restart: unless-stopped
    networks:
      - backend

volumes:
  redis_data:
  qdrant_storage:

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
