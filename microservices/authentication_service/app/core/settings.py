from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Class to hold application's config values."""

    # Basic Configurations
    SECRET_KEY: str
    ALGORITHM: str
    ACCESS_TOKEN_EXPIRES_MINUTES: int
    JWT_REFRESH_TOKEN_EXPIRES: int
    JWT_SECRET_KEY: str
    SECURITY_SALT: str

    # Database configurations
    PROD_DATABASE_URI: str
    LOCAL_DATABASE_URI: str
    TESTING_DATABASE_URI: str

    # Email Configurations
    MAIL_USERNAME: str
    MAIL_PASSWORD: str
    MAIL_FROM: str
    MAIL_FROM_NAME: str
    MAIL_PORT: int
    MAIL_SERVER: str
    TEMPLATE_FOLDER: str = "app/templates"

    # Environment
    ENV: str

    # Redirections
    REDIRECT_URL: str
    FRONTEND_INVITE_LINK: str
    AUTH_SERVICE_URL: str
    SETTINGS_SERVICE_URL: str

    GOOGLE_CLIENT_ID: str
    OPENAI_API_KEY: str
    GEMINI_API_KEY: str

    class Config:
        env_file = ".env"
        extra = "ignore"


settings = Settings()
