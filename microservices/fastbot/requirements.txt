aiofiles==24.1.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
aiosqlite==0.21.0
alembic==1.14.1
amqp==5.3.1
annotated-types==0.7.0
anyio==4.8.0
async-timeout==5.0.1
asyncpg==0.30.0
attrs==25.1.0
beautifulsoup4==4.13.1
billiard==4.2.1
cachetools==5.5.1
celery==5.4.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
Crawl4AI
cryptography==44.0.0
cssselect==1.2.0
distro==1.9.0
docstring_parser==0.16
ecdsa==0.19.0
fake-http-header==0.3.5
fake-useragent==2.0.3
fastapi==0.115.8
filelock==3.17.0
frozenlist==1.5.0
fsspec==2025.2.0
google-ai-generativelanguage==0.6.15
google-api-core==2.24.1
google-api-python-client==2.160.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.71.1
google-cloud-bigquery==3.29.0
google-cloud-core==2.4.1
google-cloud-resource-manager==1.14.0
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-genai
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
greenlet==3.1.1
grpc-google-iam-v1==0.14.0
grpcio==1.70.0
grpcio-status==1.70.0
grpcio-tools==1.70.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.7
httplib2==0.22.0
httpx
huggingface-hub==0.28.1
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
Jinja2==3.1.5
jiter==0.8.2
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kombu==5.4.2
langchain-core==0.3.33
langchain-ollama==0.2.3
langsmith==0.3.4
litellm==1.60.0
lxml==5.3.0
Mako==1.3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
mpmath==1.3.0
multidict==6.1.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.2
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==9.1.0.70
nvidia-cufft-cu12==11.2.1.3
nvidia-curand-cu12==10.3.5.147
nvidia-cusolver-cu12==11.6.1.9
nvidia-cusparse-cu12==12.3.1.170
nvidia-cusparselt-cu12==0.6.2
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
ollama==0.4.7
openai==1.61.0
orjson==3.10.15
packaging==24.2
pandas==2.2.3
pillow==10.4.0
playwright==1.50.0
portalocker==2.10.1
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.26.0
protobuf==5.29.3
psutil==6.1.1
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.7.1
pydantic_core==2.27.2
pyee==12.1.1
Pygments==2.19.1
PyMuPDF==1.25.2
pyOpenSSL==25.0.0
pyparsing==3.2.1
pytesseract==0.3.13
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-jose==3.3.0
python-multipart==0.0.20
pytz==2025.1
PyYAML==6.0.2
qdrant-client==1.13.2
rank-bm25==0.2.2
redis==5.2.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.22.3
rsa==4.9
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.1
sentence-transformers==3.4.1
setuptools==75.8.0
shapely==2.0.7
six==1.17.0
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.6
SQLAlchemy==2.0.37
starlette==0.45.3
sympy==1.13.1
tenacity==9.0.0
tf-playwright-stealth==1.1.1
threadpoolctl==3.5.0
tiktoken==0.8.0
tokenizers==0.21.0
torch==2.6.0
tqdm==4.67.1
transformers==4.48.2
triton==3.2.0
typing_extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.34.0
validators==0.34.0
vertexai==1.71.1
vine==5.1.0
wcwidth==0.2.13
websockets==14.2
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
