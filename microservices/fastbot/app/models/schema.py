from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

from pydantic import BaseModel, Field


# Pydantic Models for API requests/responses
class ChatRequest(BaseModel):
    message: str
    thread_id: Optional[str] = None
    web: Optional[bool] = False
    file_ids: Optional[List[str]] = None


class ChatResponse(BaseModel):
    thread_id: str
    title: Optional[str] = None
    history: List[Dict[str, Any]]
    reply: str


class TrialChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    web: Optional[bool] = False
    file_ids: Optional[List[str]] = None
    # Optional personalization fields
    industry_or_niche: Optional[str] = None
    company_brand_name: Optional[str] = None
    company_brand_description: Optional[str] = None


class TrialChatResponse(BaseModel):
    thread_id: str
    title: Optional[str] = None
    history: List[Dict[str, Any]]
    reply: str
    trials_remaining: int
    session_id: str


class FileUploadResponse(BaseModel):
    file_id: str
    file_name: str


class ScrapeRequest(BaseModel):
    urls: List[str]


# Define a Pydantic model for a single message in the history.
class MessageResponse(BaseModel):
    role: str
    message: str
    timestamp: datetime
    file_ids: Optional[List[str]] = None


# Define a Pydantic model for a thread with its messages.
class ThreadHistoryResponse(BaseModel):
    thread_id: str
    title: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    history: List[MessageResponse]


class FileResponse(BaseModel):
    id: str
    filename: str
    filesize: int
    filetype: str
    user_id: str
    username: Optional[str] = None
    organisation_id: str
    summary: str
    upload_date: datetime
    timestamp: int

    class Config:
        from_attributes = True


class KnowledgeBaseRequest(BaseModel):
    business_name: str
    industry: str
    business_size: str
    business_description: str
    target_audience: str
    competitor_names: list[str]
    competitor_website: list[str] = []
    brand_voice: str
    business_tone: str


class AddToKnowledgeBaseResponse(BaseModel):
    success: bool
    message: str


# Social Media Content Repurposing Models
class SocialMediaPlatform(str, Enum):
    TWITTER = "twitter"
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"
    INSTAGRAM = "instagram"


class ContentType(str, Enum):
    BLOG_POST = "blog_post"
    ARTICLE = "article"
    VIDEO_SCRIPT = "video_script"
    PODCAST_TRANSCRIPT = "podcast_transcript"
    PRESS_RELEASE = "press_release"
    GENERAL_TEXT = "general_text"


class ToneStyle(str, Enum):
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    FRIENDLY = "friendly"
    AUTHORITATIVE = "authoritative"
    HUMOROUS = "humorous"
    INSPIRATIONAL = "inspirational"


class ContentRepurposeRequest(BaseModel):
    content: str = Field(..., description="Original content to repurpose")
    platforms: List[SocialMediaPlatform] = Field(..., description="Target social media platforms")
    content_type: ContentType = Field(default=ContentType.GENERAL_TEXT, description="Type of original content")
    tone: ToneStyle = Field(default=ToneStyle.PROFESSIONAL, description="Desired tone for repurposed content")
    target_audience: Optional[str] = Field(None, description="Target audience description")
    brand_voice: Optional[str] = Field(None, description="Brand voice guidelines")
    include_hashtags: bool = Field(default=True, description="Whether to include relevant hashtags")
    include_call_to_action: bool = Field(default=True, description="Whether to include call-to-action")
    organisation_id: str = Field(..., description="Organization ID for tracking")
    user_id: str = Field(..., description="User ID for tracking")


class SocialMediaPost(BaseModel):
    platform: SocialMediaPlatform
    content: str
    hashtags: Optional[List[str]] = None
    character_count: int
    estimated_engagement: Optional[str] = None
    best_posting_time: Optional[str] = None


class ContentRepurposeResponse(BaseModel):
    task_id: str
    message: str
    organisation_id: str


class ContentRepurposeResult(BaseModel):
    original_content: str
    repurposed_posts: List[SocialMediaPost]
    processing_time: float
    created_at: datetime
    organisation_id: str

class BulkContentRepurposeRequest(BaseModel):
    contents: List[str] = Field(..., max_items=10, description="List of content to repurpose (max 10)")
    platforms: List[SocialMediaPlatform] = Field(..., description="Target social media platforms")
    content_type: ContentType = Field(default=ContentType.GENERAL_TEXT, description="Type of original content")
    tone: ToneStyle = Field(default=ToneStyle.PROFESSIONAL, description="Desired tone for repurposed content")
    target_audience: Optional[str] = Field(None, description="Target audience description")
    brand_voice: Optional[str] = Field(None, description="Brand voice guidelines")
    include_hashtags: bool = Field(default=True, description="Whether to include relevant hashtags")
    include_call_to_action: bool = Field(default=True, description="Whether to include call-to-action")
    organisation_id: str = Field(..., description="Organization ID for tracking")
    user_id: str = Field(..., description="User ID for tracking")


class BulkContentRepurposeResult(BaseModel):
    results: List[ContentRepurposeResult]
    total_processed: int
    processing_time: float
    created_at: datetime


# Simple Social Media Content Repurposing Models
class SimpleContentRepurposeRequest(BaseModel):
    content: str = Field(..., description="Original content to repurpose")
    platforms: List[SocialMediaPlatform] = Field(..., description="Target social media platforms")


class SimpleContentRepurposeResponse(BaseModel):
    repurposed_content: Dict[str, str] = Field(..., description="Platform-specific repurposed content")
