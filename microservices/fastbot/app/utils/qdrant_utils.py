import json

import redis.asyncio as redis
from app.core.config import settings
# from app.utils.embedding import get_embedding  
from app.gen_models.gemini_model import get_embedding
from app.utils.logger import get_logger
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, PointStruct, VectorParams

logger = get_logger(__name__)

VECTOR_DIM, QDRANT_HOST, QDRANT_PORT = (
    int(settings.VECTOR_DIM),
    settings.QDRANT_HOST,
    settings.QDRANT_PORT,
)

# Initialize Qdrant Client (could also be moved to a singleton in __init__.py)
qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
redis_client = redis.from_url(
    settings.REDIS_URL, encoding="utf8", decode_responses=True
)


def ensure_qdrant_collection(organization_id: str):
    """Ensure Qdrant collection exists or create it."""
    KNOWLEDGEBASE_COLLECTION = f"kb_{organization_id}"
    try:
        _ = qdrant_client.get_collection(collection_name=KNOWLEDGEBASE_COLLECTION)
        logger.info("Qdrant collection '%s' exists.", KNOWLEDGEBASE_COLLECTION)
    except Exception as e:
        logger.warning("Collection not found; creating collection: %s", e)
        qdrant_client.recreate_collection(
            collection_name=KNOWLEDGEBASE_COLLECTION,
            vectors_config=VectorParams(size=VECTOR_DIM, distance=Distance.COSINE),
        )
        logger.info("Qdrant collection '%s' created.", KNOWLEDGEBASE_COLLECTION)
    finally:
        return KNOWLEDGEBASE_COLLECTION


def add_embedding_to_qdrant(
    organization_id: str, doc_id: str, embedding: list, metadata: dict
):
    try:
        KNOWLEDGEBASE_COLLECTION = ensure_qdrant_collection(organization_id)
        logger.info(
            f"Upserting document {doc_id} into Qdrant for org {organization_id} with knowledgebase {KNOWLEDGEBASE_COLLECTION}."
        )

        point = PointStruct(
            id=doc_id,
            vector=embedding,
            payload={"organization_id": organization_id, **metadata},
        )
        qdrant_client.upsert(collection_name=KNOWLEDGEBASE_COLLECTION, points=[point])
        logger.info(
            "Upserted document %s for organization %s.", doc_id, organization_id
        )
    except Exception as e:
        logger.error("Error upserting into Qdrant: %s", e)
        raise


def remove_embedding_from_qdrant(
    organization_id: str, doc_id: str,
):
    try:
        KNOWLEDGEBASE_COLLECTION = ensure_qdrant_collection(organization_id)
        logger.info(
            f"Removing document {doc_id} from Qdrant for org {organization_id} with knowledgebase {KNOWLEDGEBASE_COLLECTION}."
        )

        qdrant_client.delete(
            collection_name=KNOWLEDGEBASE_COLLECTION,
            points_selector=[doc_id]
        )
        logger.info(
            "Deleted document %s for organization %s.", doc_id, organization_id
        )
    except Exception as e:
        logger.error("Error removing from Qdrant: %s", e)
        raise


async def get_knowledge_from_kb(organization_id: str, query: str, top: int = 5) -> list:
    """
    Retrieve relevant knowledge entries from Qdrant.
    Assumes each point payload has a 'text_snippet' field.
    """
    KNOWLEDGEBASE_COLLECTION = ensure_qdrant_collection(organization_id)
    logger.info(
        f"Searching KB for query '{query}' in org {organization_id} of knowledgebase {KNOWLEDGEBASE_COLLECTION}."
    )
    cache_key = f"kb_search:{organization_id}:{query}:{top}"
    cached = await redis_client.get(cache_key)
    if cached:
        logger.info("KB cache hit for query '%s' org %s.", query, organization_id)
        return json.loads(cached)

    try:
        query_embedding = await get_embedding(query)
    except Exception as e:
        logger.error("Error generating embedding for KB query: %s", e)
        return []

    try:
        results = qdrant_client.search(
            collection_name=KNOWLEDGEBASE_COLLECTION,
            query_vector=query_embedding,
            limit=top,
            with_payload=True,
        )
    except Exception as e:
        logger.error("Error querying Qdrant for KB: %s", e)
        return []

    knowledge_results = [
        res.payload.get("text_snippet", "")
        for res in results
        if res.payload.get("text_snippet")
    ]
    await redis_client.set(cache_key, json.dumps(knowledge_results), ex=60)
    return knowledge_results


def add_org_info_to_qdrant(organisation_id: str, org_info: dict):
    """
    Add organization info as a fixed document (with id "org_info") in the organisation's Qdrant collection.
    If the document already exists, an exception is raised.
    """
    collection_name = ensure_qdrant_collection(organisation_id)
    doc_id = "org_info"
    # Attempt to retrieve the document. (This example assumes that a missing document throws an Exception.)
    try:
        existing = qdrant_client.retrieve(collection_name=collection_name, id=doc_id)
        if existing:
            raise Exception("Organization info already exists.")
    except Exception:
        # If retrieval fails (e.g. document not found), we proceed.
        pass

    # Upsert the organization info with a dummy vector (since no embedding is needed)
    point = PointStruct(
        id=doc_id,
        vector=[0.0] * VECTOR_DIM,
        payload=org_info,
    )
    qdrant_client.upsert(collection_name=collection_name, points=[point])
    logger.info("Organization info added to collection %s.", collection_name)


def update_org_info_in_qdrant(organisation_id: str, org_info: dict):
    """
    Update the organization info in Qdrant. (The document id remains fixed as "org_info".)
    """
    collection_name = ensure_qdrant_collection(organisation_id)
    doc_id = "org_info"
    point = PointStruct(
        id=doc_id,
        vector=[0.0] * VECTOR_DIM,
        payload=org_info,
    )
    qdrant_client.upsert(collection_name=collection_name, points=[point])
    logger.info("Organization info updated in collection %s.", collection_name)
