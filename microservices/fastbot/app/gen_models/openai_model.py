import json
import os
from typing import Dict, List
from openai import OpenAI
from app.utils.logger import get_logger
from openai import Async<PERSON>penAI
from dotenv import load_dotenv
import tracemalloc
tracemalloc.start()

load_dotenv()

logger = get_logger(__name__)

client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))


async def get_title_and_summary(chunk: str, url: str) -> Dict[str, str]:
    """
    Asynchronously extract title and summary from a text chunk using OpenAI.
    Uses a system prompt to instruct the LLM.
    """
    system_prompt = (
        "You are an AI that extracts titles and summaries from documentation chunks. "
        "Return a JSON object with 'title' and 'summary' keys. "
        "For the title: If this seems like the start of a document, extract its title. "
        "If it's a middle chunk, derive a descriptive title. "
        "For the summary: Create a concise summary of the main points in this chunk. "
        "Keep both concise but informative."
    )

    try:
        response = await client.chat.completions.create(
            model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": f"URL: {url}\n\nContent:\n{chunk[:1000]}...",
                },
            ],
        )
        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error("Error getting title and summary: %s", e)
        return {
            "title": "Error processing title",
            "summary": "Error processing summary",
        }


async def get_embedding(text: str) -> List[float]:
    """Asynchronously get an embedding vector from OpenAI."""
    try:
        response = await client.embeddings.create(
            model="text-embedding-3-small", input=text
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error("Error getting embedding: %s", e)
        return [0.0] * 1536  # Adjust VECTOR_DIM if necessary


async def generate_image(prompt: str):
    """
    Generates an image using the OpenAI DALL-E API.
    """
    try:
        # Check if API key is available
        if not os.getenv("OPENAI_API_KEY"):
            raise ValueError("OpenAI API key is not configured")

        # Generate the image
        response = client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            size="1024x1024",
            quality="standard",
            n=1
        )
        images_response = await response
        # Extract the URL from the first image in the data list
        if images_response.data and len(images_response.data) > 0:
            image_url = images_response.data[0].url
            logger.info(f"Successfully generated image URL: {image_url}")
            return image_url
        else:
            logger.error("No image data returned from OpenAI API")
            raise ValueError("No image data returned from OpenAI API")
    except Exception as e:
        # Provide more specific error messages based on the exception type
        error_msg = str(e)
        if "403" in error_msg or "Forbidden" in error_msg:
            logger.error("OpenAI API access forbidden - check API key and billing status")
            raise ValueError("Image generation failed")
        elif "401" in error_msg or "Unauthorized" in error_msg:
            logger.error("OpenAI API unauthorized - invalid API key")
            raise ValueError("Image generation failed")
        elif "429" in error_msg or "rate limit" in error_msg.lower():
            logger.error("OpenAI API rate limit exceeded")
            raise ValueError("Image generation failed: Rate limit exceeded. Please try again later.")
        elif "content_policy" in error_msg.lower() or "safety" in error_msg.lower():
            logger.error("OpenAI content policy violation")
            raise ValueError("Image generation failed: Content violates OpenAI's usage policies.")
        else:
            logger.error(f"Error generating image with OpenAI DALL-E: {e}")
            raise ValueError(f"Image generation failed: {error_msg}")

   