import asyncio
import json
import mimetypes
import uuid
from datetime import datetime, timedelta
from io import Bytes<PERSON>
from typing import Annotated, Dict, List, Optional

from fastapi.responses import StreamingResponse
import redis.asyncio as redis
from app.core.config import settings
from app.database.database import get_db_session
from app.gen_models.gemini_model import (
    analyse_prompt_image_generation,
    generate_title,
    upload_file_to_gemini,
    client as gemini_client
)
from app.models.models import Chat<PERSON>essage, ChatThread, GeminiFile
from app.models.schema import (
    ChatRequest,
    ChatResponse,
    DeleteThreadResponse,
    FileUploadResponse,
    MessageResponse,
    ThreadHistoryResponse,
    TrialChatRequest,
    TrialChatResponse,
    UpdateThreadTitleRequest,
    UpdateThreadTitleResponse
)
from pydantic import BaseModel
from app.utils.dependency import get_current_user
from app.utils.external_calls import (
    get_organisation_info_cached,
    get_user_info_cached,
    verify_organization,
)
from app.utils.logger import get_logger
from app.utils.qdrant_utils import get_knowledge_from_kb
from app.utils.success_response import fail_response, success_response
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.gen_models.openai_model import generate_image
import tracemalloc
tracemalloc.start()

MODEL_PROVIDER, REDIS_URL = (settings.MODEL_PROVIDER, settings.REDIS_URL)

router = APIRouter()
logger = get_logger(__name__)

# Initialize Redis client with error handling
try:
    logger.info(f"Initializing Redis client with URL: {REDIS_URL}")
    redis_client = redis.from_url(REDIS_URL, encoding="utf8", decode_responses=True)
    logger.info("Redis client initialized successfully")
except Exception as e:
    logger.error(f"Error initializing Redis client: {str(e)}")
    # Create a dummy Redis client that won't crash the app
    class DummyRedisClient:
        async def get(self, key):
            logger.warning(f"Dummy Redis GET operation for key: {key}")
            return None

        async def set(self, key, value, ex=None):
            logger.warning(f"Dummy Redis SET operation for key: {key}, value: {value}, expiry: {ex}")
            return True

        async def delete(self, key):
            logger.warning(f"Dummy Redis DELETE operation for key: {key}")
            return True

    redis_client = DummyRedisClient()
    logger.info("Using dummy Redis client as fallback")

STREAM_RESPONSE = True

if MODEL_PROVIDER.lower() == "openai":
    from app.gen_models.openai_model import generate_reply as generate_reply_openai
elif MODEL_PROVIDER.lower() == "gemini":
    from app.gen_models.gemini_model import generate_reply as generate_reply_gemini
elif MODEL_PROVIDER.lower() == "deepseek":
    from app.gen_models.deepseek_model import generate_reply as generate_reply_deepseek
else:
    raise ValueError("Invalid MODEL_PROVIDER specified in the .env file.")


async def create_new_thread(
    db: AsyncSession,
    title: str,
    organization_id: str,
    user_id: str,
    initial_message: str,
) -> str:
    try:
        thread = ChatThread(
            title=title,
            organization_id=organization_id,
            user_id=user_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        db.add(thread)
        await db.commit()
        await db.refresh(thread)
        await store_chat_message(db, thread.id, "user", initial_message)
        logger.info("Created new chat thread %s for organization %s.", thread.id, organization_id)
        return thread.id
    except Exception as e:
        logger.error("Error generating thread title: %s", str(e))
        raise


async def store_chat_message(db: AsyncSession, thread_id: str, role: str, message: str, file_ids: List[str] = None):
    try:
        # Create a ChatMessage with the current timestamp
        # Note: We're using a naive datetime here because the database expects it
        # The timezone handling is done at the database level
        msg = ChatMessage(
            thread_id=thread_id,
            role=role,
            message=message,
            file_ids=",".join(file_ids) if file_ids else None,
            timestamp=datetime.now(),
        )
        db.add(msg)
        await db.commit()
        cache_key = f"chat_history:{thread_id}"
        try:
            await redis_client.delete(cache_key)
            logger.info("Stored message for thread %s and invalidated cache.", thread_id)
        except Exception as redis_error:
            # If Redis fails, log the error but don't fail the whole operation
            logger.error("Error invalidating Redis cache: %s", str(redis_error))
            logger.info("Stored message for thread %s but failed to invalidate cache.", thread_id)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error("Error storing chat messages: %s\nTraceback: %s", str(e), error_traceback)
        raise


async def get_chat_history(db: AsyncSession, thread_id: str) -> List[Dict[str, any]]:
    try:
        cache_key = f"chat_history:{thread_id}"
        try:
            cached = await redis_client.get(cache_key)
            if cached:
                logger.info("Cache hit for chat history of thread %s.", thread_id)
                return json.loads(cached)
        except Exception as redis_error:
            logger.error("Error checking Redis cache: %s", str(redis_error))
            # Continue with database query if Redis fails

        logger.info("Cache miss for chat history of thread %s. Querying PostgreSQL.", thread_id)

        result = await db.execute(
            select(ChatMessage)
            .where(ChatMessage.thread_id == thread_id)
            .order_by(ChatMessage.timestamp)
        )
        messages = result.scalars().all()
        MAX_MESSAGES = 50
        trimmed = messages[-MAX_MESSAGES:] if len(messages) > MAX_MESSAGES else messages

        history = []
        for msg in trimmed:
            message_data = {"role": msg.role, "message": msg.message}
            if msg.file_ids:
                message_data["file_ids"] = msg.file_ids.split(",")
            history.append(message_data)

        try:
            await redis_client.set(cache_key, json.dumps(history), ex=60)
        except Exception as redis_error:
            logger.error("Error setting Redis cache: %s", str(redis_error))
            # Continue even if Redis set fails

        return history
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error("Error retrieving chat history: %s\nTraceback: %s", str(e), error_traceback)
        # Return an empty history instead of raising an exception
        return []


async def generate_chat_reply(history, user_message, file_ids=None):
    """
    Unified function to generate a reply based on conversation history and the latest user message.
    For OpenAI, we expect history to be a list of messages.
    For Gemini, we convert the history to a prompt string.

    Parameters:
      - history: The conversation history
      - user_message: The latest user message
      - file_ids: Optional list of file IDs to include in the prompt
    """
    try:
        if MODEL_PROVIDER.lower() == "openai":
            messages = []
            for entry in history:
                content = entry.get("content", entry.get("message", ""))
                messages.append({"role": entry["role"], "content": content})
            messages.append({"role": "user", "content": user_message})
            return await generate_reply_openai(messages)

        elif MODEL_PROVIDER.lower() == "gemini":
            # Get file objects if file_ids are provided
            files = []
            if file_ids:
                # Query the database for file information
                for file_id in file_ids:
                    try:
                        # Get the file from Gemini
                        file = gemini_client.files.get(name=file_id)
                        if file:
                            files.append(file)
                            logger.info(f"Retrieved file from Gemini: {file_id}")
                    except Exception as e:
                        logger.error(f"Error retrieving file from Gemini: {e}")

            # Build the prompt
            prompt_parts = []

            # Add system and history messages
            prompt_text = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                prompt_text += f"{entry['role'].capitalize()}: {content}\n"

            # Add the latest user message
            prompt_text += f"User: {user_message}\nAssistant:"
            prompt_parts.append({"text": prompt_text})

            # If streaming, we need to handle files differently
            if STREAM_RESPONSE:
                # Note: In this branch, thread_id and thread_title are provided in the endpoint.
                return stream_gemini_reply(prompt_text, None, None, None, files=files)
            else:
                # Use the generate_reply_gemini function with files
                from app.gen_models.gemini_model import generate_reply as generate_reply_gemini
                return generate_reply_gemini(prompt_parts, files=files)

        elif MODEL_PROVIDER.lower() == "deepseek":
            messages = []
            for entry in history:
                content = entry.get("content", entry.get("message", ""))
                messages.append({"role": entry["role"], "content": content})
            messages.append({"role": "user", "content": user_message})
            return await asyncio.to_thread(generate_reply_deepseek, messages)

        else:
            raise ValueError(f"Unsupported MODEL_PROVIDER: {MODEL_PROVIDER}")
    except Exception as e:
        logger.error("Error generating a reply to chat: %s", str(e))
        raise HTTPException(status_code=500, detail="Error generating a reply to chat")


async def stream_gemini_reply(
    prompt: str, thread_id: str, thread_title: str, db: AsyncSession, web_search=False, files=None
):
    """
    Streams the Gemini reply text chunk-by-chunk.
    Sends metadata first (thread_id, thread_title, history), then streams chunks.
    After the stream is done, the full reply is stored in the database.

    If image=True, the prompt is first analyzed for image suitability.
    If deemed suitable, an image is generated and streamed as a single chunk.

    Parameters:
      - prompt: The prompt text
      - thread_id: The thread ID
      - thread_title: The thread title
      - db: The database session
      - web_search: Whether to use web search
      - files: Optional list of file objects to include in the prompt
    """
    full_reply = ""


    # If the image flag is set, analyze the prompt and attempt image generation.
    generate_image_flag = False
    try:
        gpt_response = analyse_prompt_image_generation(prompt)
        if "yes $ellum" in gpt_response.lower():
            generate_image_flag = True
        logger.info(f"Gemini Analysis: {gpt_response}")
    except Exception as e:
        logger.error(f"Failed to analyze prompt for image generation using Gemini: {str(e)}")
    # First, yield metadata as an SSE event
    metadata = {
        "thread_id": thread_id,
        "title": thread_title,
        "type": "text" if not generate_image_flag else "image",
    }
    yield f"data: {json.dumps({'metadata': metadata})}\n\n"

    # Generate an image if the analysis indicates so
    if generate_image_flag:
        logger.info("Generate image true")
        try:
            # Await the generate_image function and ensure it's fully resolved
            bot_response = await generate_image(prompt)

            # If bot_response is still a coroutine, await it again
            if asyncio.iscoroutine(bot_response):
                bot_response = await bot_response

            # Check if bot_response has a 'data' attribute (e.g., from a library like aiohttp)
            if hasattr(bot_response, "data"):
                bot_response = bot_response.data

            # Validate the response format
            if not isinstance(bot_response, (str, bytes)):
                logger.error(f"Unexpected response format from generate_image: {type(bot_response)}")
                raise ValueError(f"Unexpected response format from generate_image: expected str or bytes, got {type(bot_response)}")

            if bot_response:
                logger.info("Image generated successfully")
                full_reply = bot_response
                # Yield the image response as a single chunk (e.g., image URL or base64 data)
                yield f"data: {json.dumps({'image': full_reply})}\n\n"
                # Store the image response and indicate completion, then exit.
                await store_chat_message(db, thread_id, "assistant", full_reply)
                yield f"data: {json.dumps({'status': 'completed'})}\n\n"
                return
            else:
                logger.warning("Image generation returned empty response")
                raise ValueError("Image generation returned empty response")
        except Exception as e:
            logger.error(f"Error generating or downloading the image: {str(e)}")
            # Instead of failing completely, fall back to text generation with an explanation
            error_message = f"I apologize, but I'm unable to generate images at the moment. {str(e)}"
            logger.info("Falling back to text response due to image generation failure")

            # Reset the generate_image_flag to continue with text generation
            generate_image_flag = False

            # Yield an informative message about the image generation failure
            yield f"data: {json.dumps({'chunk': error_message + '\\n\\nLet me provide a text response instead:\\n\\n'})}\n\n"
            full_reply += error_message + "\n\nLet me provide a text response instead:\n\n"

    # If web_search is requested, use the Gemini generation with the Google search tool.
    if web_search:
        # Use the generate_content method with the google search tool
        from google import genai
        from google.genai.types import Tool, GenerateContentConfig, GoogleSearch

        client = genai.Client(api_key=settings.GEMINI_API_KEY)
        model_id = "gemini-2.0-flash"
        google_search_tool = Tool(google_search=GoogleSearch())

        # Prepare contents with prompt and files
        contents = []
        if isinstance(prompt, str):
            contents.append({"text": prompt})
        elif isinstance(prompt, list):
            contents = prompt

        # Add files if provided
        if files and isinstance(files, list):
            for file in files:
                if file:
                    contents.append(file)

        response = client.models.generate_content(
            model=model_id,
            contents=contents,
            config=GenerateContentConfig(
                tools=[google_search_tool],
                response_modalities=["TEXT"],
            )
        )

        # Iterate over the returned content parts and yield them as SSE chunks.
        for part in response.candidates[0].content.parts:
            text = part.text
            full_reply += text
            yield f"data: {json.dumps({'chunk': text})}\n\n"

    else:
        # Use the chat stream generator as before
        def get_stream_generator():
            from google import genai
            client = genai.Client(api_key=settings.GEMINI_API_KEY)

            # Create a chat with the model
            chat = client.chats.create(model="gemini-2.0-flash")

            # Prepare the message content
            message_content = []

            # Add text prompt
            if isinstance(prompt, str):
                message_content.append({"text": prompt})
            elif isinstance(prompt, list):
                message_content = prompt

            # Add files if provided
            if files and isinstance(files, list):
                for file in files:
                    if file:
                        message_content.append(file)

            # Send the message with content
            return chat.send_message_stream(message_content)

        # Get the blocking generator in a separate thread
        try:
            gen_obj = await asyncio.to_thread(get_stream_generator)

            # Helper to get the next chunk asynchronously
            async def async_next():
                try:
                    return await asyncio.to_thread(lambda: next(gen_obj, None))
                except Exception as e:
                    logger.error(f"Error getting next chunk: {str(e)}")
                    return None

            # Stream the response chunks as SSE events
            while True:
                try:
                    chunk = await async_next()
                    if chunk is None:
                        break
                    text = chunk.text
                    full_reply += text
                    yield f"data: {json.dumps({'chunk': text})}\n\n"
                except Exception as e:
                    logger.error(f"Error processing chunk: {str(e)}")
                    # Yield an error message and break the loop
                    yield f"data: {json.dumps({'error': f'Error processing response: {str(e)}'})}\n\n"
                    break
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Error in stream generation: {str(e)}\nTraceback: {error_traceback}")
            # Yield an error message
            yield f"data: {json.dumps({'error': f'Error generating response: {str(e)}'})}\n\n"

    # After streaming, store the full reply in the DB
    await store_chat_message(db, thread_id, "assistant", full_reply)
    # Indicate completion
    yield f"data: {json.dumps({'status': 'completed'})}\n\n"



@router.post("/new", response_model=ChatResponse)
async def new_chat(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    chat_request: ChatRequest = ...,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Start a new chat thread.
    When using Gemini the assistant’s reply is streamed back along with metadata.
    """
    try:
        user_id = token["decoded"].get("user_id")
        organisation_info = await get_organisation_info_cached(organization_id)
        user_info = await get_user_info_cached(organization_id, user_id)
        logger.info(f"User {user_id} is starting a new chat")

        # Create a new thread and store the initial user message.
        thread_title_prompt = f"Summarize this chat, in a short, meaningful title (max 20 words). '{chat_request.message}'"
        thread_title = generate_title(thread_title_prompt)
        thread_id = await create_new_thread(db, thread_title, organization_id, user_id, chat_request.message)
        history = await get_chat_history(db, thread_id)

        # Retrieve additional context from the knowledgebase.
        knowledge_results = await get_knowledge_from_kb(organization_id, chat_request.message, redis_client)
        logger.info(f"Knowledge results: {knowledge_results}")

        system_context = (
            "Relevant knowledge from the knowledge base you should always be aware of:\n"
            + "\n".join(knowledge_results)
            + f"\n\nAdditionally, here are the organisation details: {organisation_info} along with my details: {user_info}"
            + "\n\nEnsure your response aligns with my roles in the organization"
        )
        history.insert(0, {"role": "system", "content": system_context})

        web_search = True if chat_request.web else False

        # Get file objects if file_ids are provided
        file_ids = chat_request.file_ids

        # Store the user message with file IDs
        await store_chat_message(db, thread_id, "user", chat_request.message, file_ids)

        if MODEL_PROVIDER.lower() == "gemini" and STREAM_RESPONSE:
            # Build the Gemini prompt from history
            prompt = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                prompt += f"{entry['role'].capitalize()}: {content}\n"
            prompt += f"User: {chat_request.message}\nAssistant:"
            # Get file objects if file_ids are provided
            files = []
            if file_ids:
                # Query the database for file information
                for file_id in file_ids:
                    try:
                        # Get the file from Gemini
                        file = gemini_client.files.get(name=file_id)
                        if file:
                            files.append(file)
                            logger.info(f"Retrieved file from Gemini for chat: {file_id}")
                    except Exception as e:
                        logger.error(f"Error retrieving file from Gemini for chat: {e}")

            # Pass thread_id, thread_title, into the stream generator.
            stream = stream_gemini_reply(prompt, thread_id, thread_title, db, web_search, files=files)
            return StreamingResponse(stream, media_type="text/event-stream")
        else:
            reply = await generate_chat_reply(history, chat_request.message, file_ids)
            await store_chat_message(db, thread_id, "assistant", reply)
            history = await get_chat_history(db, thread_id)
            logger.info(f"Started new chat thread {thread_id} for organization {organization_id}.")
            return success_response(
                200,
                "chat started",
                ChatResponse(
                    thread_id=thread_id, title=thread_title, history=history, reply=reply
                ),
            )
    except HTTPException as e:
        logger.error("HTTPException: %s", e)
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("Error starting a new chat: %s", str(e))
        return fail_response(500, "Error starting a new chat")


@router.post("/continue", response_model=ChatResponse)
async def continue_chat(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    chat_request: ChatRequest = ...,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Continue an existing chat thread.
    When using Gemini the reply is streamed back along with metadata.
    """
    try:
        user_id = token["decoded"].get("user_id")
        organisation_info = await get_organisation_info_cached(organization_id)
        user_info = await get_user_info_cached(organization_id, user_id)
        logger.info(f"User {user_id} is continuing chat in thread {chat_request.thread_id}")

        if not chat_request.thread_id:
            raise HTTPException(status_code=400, detail="thread_id is required to continue chat")

        # Get file objects if file_ids are provided
        file_ids = chat_request.file_ids

        # Store the new user message with file IDs
        await store_chat_message(db, chat_request.thread_id, "user", chat_request.message, file_ids)
        history = await get_chat_history(db, chat_request.thread_id)

        knowledge_results = await get_knowledge_from_kb(organization_id, chat_request.message)
        system_context = (
            "Relevant knowledge from the knowledge base you should always be aware of:\n"
            + "\n".join(knowledge_results)
            + f"\n\nAdditionally, here are the organisation details: {organisation_info} along with my details: {user_info}"
            + "\n\nEnsure your response aligns with my roles in the organization"
        )
        history.insert(0, {"role": "system", "content": system_context})

        web_search = True if chat_request.web else False

        if MODEL_PROVIDER.lower() == "gemini" and STREAM_RESPONSE:
            # Build the Gemini prompt from updated history
            prompt = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                prompt += f"{entry['role'].capitalize()}: {content}\n"
            prompt += f"User: {chat_request.message}\nAssistant:"
            # Get file objects if file_ids are provided
            files = []
            if file_ids:
                # Query the database for file information
                for file_id in file_ids:
                    try:
                        # Get the file from Gemini
                        file = gemini_client.files.get(name=file_id)
                        if file:
                            files.append(file)
                            logger.info(f"Retrieved file from Gemini for chat: {file_id}")
                    except Exception as e:
                        logger.error(f"Error retrieving file from Gemini for chat: {e}")

            # In this example, thread_title is not updated on continue so we pass None.
            stream = stream_gemini_reply(prompt, chat_request.thread_id, None, db, web_search, files=files)
            return StreamingResponse(stream, media_type="text/event-stream")
        else:
            reply = await generate_chat_reply(history, chat_request.message, file_ids)
            await store_chat_message(db, chat_request.thread_id, "assistant", reply)
            history = await get_chat_history(db, chat_request.thread_id)
            logger.info(f"Continued chat thread {chat_request.thread_id} for organization {organization_id}.")
            return success_response(
                200,
                "chat continued",
                ChatResponse(
                    thread_id=chat_request.thread_id, history=history, reply=reply
                ),
            )
    except HTTPException as e:
        logger.error("An exception occurred: %s", str(e))
        return fail_response(e.status_code)
    except Exception as e:
        logger.error("An unexpected error occurred: %s", str(e))
        return fail_response(500, "An unexpected error occurred")


@router.get("/threads/{thread_id}", response_model=ThreadHistoryResponse)
async def get_thread_message(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    thread_id: str,
    db: AsyncSession = Depends(get_db_session),
):
    """Retrieve the chat history in a particular thread"""
    try:
        logger.info(f"retrieving the messages in chat thread {thread_id}")
        user_id = token["decoded"].get("user_id")
        thread_stmt = select(ChatThread).where(
            ChatThread.id == thread_id,
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(thread_stmt)
        thread_data = result.scalars().first()

        logger.info("chat thread retrieved")
        if not thread_data:
            raise HTTPException(status_code=404, detail="Requested thread not found")

        logger.info(
            f"Retrieving the related chat messages for the thread id : {thread_id}"
        )

        msg_stmt = (
            select(ChatMessage)
            .where(ChatMessage.thread_id == thread_id)
            .order_by(ChatMessage.timestamp)
        )
        msg_result = await db.execute(msg_stmt)
        messages = msg_result.scalars().all()
        logger.info(messages)
        message_data = []
        for msg in messages:
            response = MessageResponse(
                role=msg.role,
                message=msg.message,
                timestamp=msg.timestamp,
            )
            if msg.file_ids:
                response.file_ids = msg.file_ids.split(",")
            message_data.append(response)

        response = ThreadHistoryResponse(
            thread_id=thread_data.id,
            title=thread_data.title,
            created_at=thread_data.created_at,
            updated_at=thread_data.updated_at,
            history=message_data,
        )

        logger.info(f"chat thread: {thread_id} messages retrived successfully")
        return success_response(200, "Message thread retrieved", response)
    except HTTPException as e:
        logger.error("An exception occurred: %s".str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("An unexpected error occurred: %s", str(e))
        return fail_response(500, "An unexpected error occurred")


@router.get("/threads", response_model=List[ThreadHistoryResponse])
async def get_all_threads(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    db: AsyncSession = Depends(get_db_session),
):
    """
    Retrieve all chat threads and their chat history for the given organization and user.
    """
    try:
        user_id = token["decoded"].get("user_id")
        # Query threads for the given organization and user.
        threads_stmt = select(ChatThread).where(
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(threads_stmt)
        threads = result.scalars().all()

        if not threads:
            raise HTTPException(
                status_code=404,
                detail="No threads found for the provided organization and user.",
            )

        thread_data = [
            {"id": thread.id, "title": thread.title, "created_at": thread.created_at}
            for thread in threads
        ]

        return success_response(200, "chat history retrieved", thread_data)
    except HTTPException as e:
        logger.error("An exception occurred: %s", str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("An unexpected error occurred: %s", str(e))
        return fail_response(500, "An unexpected error occurred")


@router.delete("/threads/{thread_id}", response_model=DeleteThreadResponse)
async def delete_chat_thread(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[dict, Depends(get_current_user)],
    thread_id: str,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Delete a chat thread and all its associated messages for the given organization and user.
    Only the user who owns the thread can delete it.
    """
    try:
        user_id = token["decoded"].get("user_id")
        logger.info(f"User {user_id} is attempting to delete chat thread {thread_id}")

        # Verify the thread exists and belongs to the user and organization
        thread_stmt = select(ChatThread).where(
            ChatThread.id == thread_id,
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(thread_stmt)
        thread = result.scalars().first()

        if not thread:
            logger.error(f"Thread {thread_id} not found for user {user_id} in organization {organization_id}")
            return fail_response(
                404,
                "Chat thread not found or you don't have permission to delete it"
            )

        # Delete all chat messages associated with this thread
        messages_stmt = select(ChatMessage).where(ChatMessage.thread_id == thread_id)
        messages_result = await db.execute(messages_stmt)
        messages = messages_result.scalars().all()

        for message in messages:
            await db.delete(message)

        # Delete the thread itself
        await db.delete(thread)
        await db.commit()

        # Clear any cached data from Redis
        try:
            cache_key = f"chat_history:{thread_id}"
            await redis_client.delete(cache_key)
            logger.info(f"Cleared cache for deleted thread {thread_id}")
        except Exception as redis_error:
            logger.error(f"Error clearing Redis cache for thread {thread_id}: {str(redis_error)}")
            # Continue even if Redis delete fails

        logger.info(f"Successfully deleted chat thread {thread_id} and {len(messages)} messages")
        return success_response(
            200,
            "Chat thread deleted successfully",
            {"thread_id": thread_id, "messages_deleted": len(messages)}
        )

    except HTTPException as e:
        logger.error(f"HTTPException occurred while deleting thread {thread_id}: {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred while deleting thread {thread_id}: {str(e)}")
        return fail_response(500, "An unexpected error occurred while deleting the chat thread")


@router.put("/threads/{thread_id}", response_model=UpdateThreadTitleResponse)
async def update_thread_title(
    organization_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[dict, Depends(get_current_user)],
    thread_id: str,
    request: UpdateThreadTitleRequest,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Update the title of a chat thread for the given organization and user.
    Only the user who owns the thread can update its title.
    """
    try:
        user_id = token["decoded"].get("user_id")
        logger.info(f"User {user_id} is updating thread {thread_id} title to '{request.new_title}'")

        # Verify the thread exists and belongs to the user and organization
        thread_stmt = select(ChatThread).where(
            ChatThread.id == thread_id,
            ChatThread.organization_id == organization_id,
            ChatThread.user_id == user_id,
        )
        result = await db.execute(thread_stmt)
        thread = result.scalars().first()

        if not thread:
            logger.error(f"Thread {thread_id} not found for user {user_id} in organization {organization_id}")
            return fail_response(
                404,
                "Chat thread not found or you don't have permission to update it"
            )

        # Store the old title for response
        old_title = thread.title

        # Update the title and updated_at timestamp
        thread.title = request.new_title
        thread.updated_at = datetime.now()
        await db.commit()

        # Clear any cached data from Redis since thread metadata changed
        try:
            cache_key = f"chat_history:{thread_id}"
            await redis_client.delete(cache_key)
            logger.info(f"Cleared cache for updated thread {thread_id}")
        except Exception as redis_error:
            logger.error(f"Error clearing Redis cache for thread {thread_id}: {str(redis_error)}")
            # Continue even if Redis delete fails

        logger.info(f"Successfully updated thread {thread_id} title from '{old_title}' to '{request.new_title}'")
        return success_response(
            200,
            "Thread title updated successfully",
            {
                "thread": {
                    "thread_id": thread.id,
                    "old_title": old_title,
                    "new_title": request.new_title,
                    "updated_at": thread.updated_at.isoformat()
                }
            }
        )

    except HTTPException as e:
        logger.error(f"HTTPException occurred while updating thread {thread_id}: {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred while updating thread {thread_id}: {str(e)}")
        return fail_response(500, "An unexpected error occurred while updating the chat thread")


async def create_trial_thread(
    db: AsyncSession,
    title: str,
    session_id: str,
    initial_message: str,
    file_ids: List[str] = None,
) -> str:
    """Create a new chat thread for trial users without authentication"""
    try:
        # For trial users, we use a temporary organization_id and user_id based on session
        thread = ChatThread(
            title=title,
            organization_id=f"trial_{session_id}",
            user_id=f"trial_{session_id}",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        db.add(thread)
        await db.commit()
        await db.refresh(thread)
        await store_chat_message(db, thread.id, "user", initial_message, file_ids)
        logger.info("Created new trial chat thread %s for session %s.", thread.id, session_id)
        return thread.id
    except Exception as e:
        logger.error("Error creating trial thread: %s", str(e))
        raise


async def store_trial_personalization(session_id: str, personalization_data: dict) -> bool:
    """Store personalization data for a trial session"""
    try:
        cache_key = f"trial_personalization:{session_id}"
        logger.info(f"Storing personalization data for session {session_id}")

        try:
            await redis_client.set(cache_key, json.dumps(personalization_data), ex=86400)  # Expire after 24 hours
            return True
        except Exception as redis_error:
            logger.error(f"Redis error when storing personalization: {str(redis_error)}")
            return False
    except Exception as e:
        logger.error("Error storing trial personalization: %s", str(e))
        return False


async def get_trial_personalization(session_id: str) -> dict:
    """Get personalization data for a trial session"""
    try:
        cache_key = f"trial_personalization:{session_id}"
        logger.info(f"Getting personalization data for session {session_id}")

        try:
            data = await redis_client.get(cache_key)
            if data:
                return json.loads(data)
        except Exception as redis_error:
            logger.error(f"Redis error when getting personalization: {str(redis_error)}")

        return {}
    except Exception as e:
        logger.error("Error getting trial personalization: %s", str(e))
        return {}


def is_unlimited_session(session_id: str) -> bool:
    """Check if a session ID is unlimited (bypasses trial limits)"""
    return session_id.startswith("unlimited_")

async def get_trials_remaining(session_id: str) -> int:
    """Get the number of trial messages remaining for a session"""
    try:
        # Check for special unlimited session ID
        if is_unlimited_session(session_id):
            logger.info(f"Unlimited session detected: {session_id}")
            return 999999  # Return a very high number to indicate unlimited

        cache_key = f"trial_count:{session_id}"
        logger.info(f"Checking Redis for trial count with key: {cache_key}")

        try:
            count = await redis_client.get(cache_key)
            logger.info(f"Redis returned count: {count}")
        except Exception as redis_error:
            logger.error(f"Redis error when getting count: {str(redis_error)}")
            # If Redis fails, default to allowing the trial
            return 4

        if count is None:
            # First time user, set the trial count to 4
            logger.info(f"First time user, setting trial count to 4 for session {session_id}")
            try:
                await redis_client.set(cache_key, "4", ex=86400)  # Expire after 24 hours
            except Exception as redis_set_error:
                logger.error(f"Redis error when setting count: {str(redis_set_error)}")
                # Continue even if Redis set fails
            return 4

        return int(count)
    except Exception as e:
        logger.error("Error getting trial count: %s", str(e))
        # Default to 4 if there's an error - allow the trial rather than block it
        return 4


async def decrement_trials_remaining(session_id: str) -> int:
    """Decrement the number of trial messages remaining and return the new count"""
    try:
        # Check for special unlimited session ID - don't decrement
        if is_unlimited_session(session_id):
            logger.info(f"Unlimited session detected, not decrementing: {session_id}")
            return 999999  # Return a very high number to indicate unlimited

        cache_key = f"trial_count:{session_id}"
        logger.info(f"Decrementing trial count for session {session_id}")

        try:
            count = await redis_client.get(cache_key)
            logger.info(f"Current count before decrement: {count}")
        except Exception as redis_error:
            logger.error(f"Redis error when getting count for decrement: {str(redis_error)}")
            # If Redis fails, assume 3 remaining (first message used)
            return 3

        if count is None:
            # Should not happen, but handle it gracefully
            logger.info(f"No count found for decrement, setting to 3 for session {session_id}")
            try:
                await redis_client.set(cache_key, "3", ex=86400)  # Expire after 24 hours
            except Exception as redis_set_error:
                logger.error(f"Redis error when setting count after decrement: {str(redis_set_error)}")
            return 3

        new_count = max(0, int(count) - 1)
        logger.info(f"New count after decrement: {new_count}")

        try:
            await redis_client.set(cache_key, str(new_count), ex=86400)  # Refresh expiry
        except Exception as redis_set_error:
            logger.error(f"Redis error when updating count after decrement: {str(redis_set_error)}")
            # Continue even if Redis set fails

        return new_count
    except Exception as e:
        logger.error("Error decrementing trial count: %s", str(e))
        # Return 3 instead of 0 to allow the trial to continue
        return 3


@router.post("/trial", response_model=TrialChatResponse)
async def trial_chat(
    chat_request: TrialChatRequest,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Start or continue a trial chat without authentication.
    Users get 4 messages before being prompted to register.

    Optional Personalization Fields:
    - industry_or_niche: The user's industry or niche (e.g., "Tech", "Fashion", "Health")
    - company_brand_name: The name of the user's company or brand
    - company_brand_description: A brief description or mission of the company/brand

    These fields help the AI provide more personalized and relevant responses.

    Frontend Implementation Guide:
    1. For a new user, call this endpoint without a session_id
    2. Store the returned session_id in localStorage/sessionStorage
    3. For subsequent requests, include the stored session_id
    4. Track trials_remaining to show appropriate UI prompts
    5. When the user registers, call /transfer-trial-chats with the session_id
    6. Optionally include personalization fields to improve response quality
    """
    try:
        # Use the provided session_id or generate a new one
        session_id = chat_request.session_id or str(uuid.uuid4())

        # Store personalization data if provided (for new sessions or updates)
        personalization_data = {}
        if chat_request.industry_or_niche:
            personalization_data['industry_or_niche'] = chat_request.industry_or_niche
        if chat_request.company_brand_name:
            personalization_data['company_brand_name'] = chat_request.company_brand_name
        if chat_request.company_brand_description:
            personalization_data['company_brand_description'] = chat_request.company_brand_description

        if personalization_data:
            await store_trial_personalization(session_id, personalization_data)

        # Check if the user has trials remaining
        try:
            trials_remaining = await get_trials_remaining(session_id)
            logger.info(f"Trials remaining for session {session_id}: {trials_remaining}")

            if trials_remaining <= 0:
                return fail_response(
                    403,
                    "You've used all your trial messages. Please register and log in to continue chatting.",
                    {"trials_remaining": 0, "session_id": session_id}
                )

            # Decrement the trial count
            new_trials_remaining = await decrement_trials_remaining(session_id)
            logger.info(f"New trials remaining after decrement: {new_trials_remaining}")
        except Exception as e:
            logger.error(f"Error handling trial count: {str(e)}")
            # If there's an error, allow the trial to continue with a default count
            new_trials_remaining = 3  # Assume this is the first message

        # Get file objects if file_ids are provided
        file_ids = chat_request.file_ids

        # If this is a new chat (no thread_id), create a new thread
        if not chat_request.session_id:
            # Generate a title for the thread
            thread_title_prompt = f"Summarize this chat, in a short, meaningful title (max 20 words). '{chat_request.message}'"
            thread_title = generate_title(thread_title_prompt)
            thread_id = await create_trial_thread(db, thread_title, session_id, chat_request.message, file_ids)
            history = await get_chat_history(db, thread_id)
        else:
            # Continue an existing chat
            # Get the thread_id from the session_id
            thread_stmt = select(ChatThread).where(
                ChatThread.user_id == f"trial_{session_id}"
            ).order_by(ChatThread.created_at.desc())
            result = await db.execute(thread_stmt)
            thread = result.scalars().first()

            if not thread:
                # If no thread is found, create a new one
                thread_title_prompt = f"Summarize this chat, in a short, meaningful title (max 20 words). '{chat_request.message}'"
                thread_title = generate_title(thread_title_prompt)
                thread_id = await create_trial_thread(db, thread_title, session_id, chat_request.message, file_ids)
            else:
                thread_id = thread.id
                thread_title = thread.title
                # Store the new user message with file IDs
                await store_chat_message(db, thread_id, "user", chat_request.message, file_ids)

            history = await get_chat_history(db, thread_id)

        # For trial users, build personalized context using both current request and stored data
        system_context_parts = ["You are a helpful AI assistant. This is a trial conversation."]

        # Get stored personalization data for this session
        stored_personalization = await get_trial_personalization(session_id)

        # Merge current request data with stored data (current request takes precedence)
        effective_personalization = {**stored_personalization}
        if chat_request.industry_or_niche:
            effective_personalization['industry_or_niche'] = chat_request.industry_or_niche
        if chat_request.company_brand_name:
            effective_personalization['company_brand_name'] = chat_request.company_brand_name
        if chat_request.company_brand_description:
            effective_personalization['company_brand_description'] = chat_request.company_brand_description

        # Add personalization context if available
        if effective_personalization.get('industry_or_niche'):
            system_context_parts.append(f"The user works in the {effective_personalization['industry_or_niche']} industry/niche.")

        if effective_personalization.get('company_brand_name'):
            system_context_parts.append(f"The user represents {effective_personalization['company_brand_name']}.")

        if effective_personalization.get('company_brand_description'):
            system_context_parts.append(f"About the company/brand: {effective_personalization['company_brand_description']}")

        # Add trial information
        system_context_parts.append(f"The user has {new_trials_remaining} trial messages remaining.")
        system_context_parts.append("You can suggest registering for a full account to access additional features.")

        # Add personalization instructions
        if any(effective_personalization.values()):
            system_context_parts.append("Please tailor your responses to be relevant to their industry and company context.")

        system_context = " ".join(system_context_parts)
        history.insert(0, {"role": "system", "content": system_context})

        web_search = True if chat_request.web else False

        if MODEL_PROVIDER.lower() == "gemini" and STREAM_RESPONSE:
            # Build the Gemini prompt from history
            prompt = "Conversation so far:\n"
            for entry in history:
                content = entry.get("content", entry.get("message", "[No content]"))
                prompt += f"{entry['role'].capitalize()}: {content}\n"
            prompt += f"User: {chat_request.message}\nAssistant:"

            # First, yield the session_id as a separate event
            async def session_id_stream():
                try:
                    # Yield session_id first
                    yield f"data: {json.dumps({'session_id': session_id, 'trials_remaining': new_trials_remaining})}\n\n"

                    # Get file objects if file_ids are provided
                    files = []
                    if file_ids:
                        # Query the database for file information
                        for file_id in file_ids:
                            try:
                                # Get the file from Gemini
                                file = gemini_client.files.get(name=file_id)
                                if file:
                                    files.append(file)
                                    logger.info(f"Retrieved file from Gemini for chat: {file_id}")
                            except Exception as e:
                                logger.error(f"Error retrieving file from Gemini for chat: {e}")
                                # Continue without this file

                    # Then yield the regular stream
                    try:
                        async for chunk in stream_gemini_reply(prompt, thread_id, thread_title, db, web_search, files=files):
                            yield chunk
                    except Exception as e:
                        import traceback
                        error_traceback = traceback.format_exc()
                        logger.error(f"Error in stream_gemini_reply: {str(e)}\nTraceback: {error_traceback}")
                        yield f"data: {json.dumps({'error': f'Error generating response: {str(e)}'})}\n\n"
                        yield f"data: {json.dumps({'status': 'completed'})}\n\n"
                except Exception as e:
                    import traceback
                    error_traceback = traceback.format_exc()
                    logger.error(f"Error in session_id_stream: {str(e)}\nTraceback: {error_traceback}")
                    yield f"data: {json.dumps({'error': f'Error processing request: {str(e)}'})}\n\n"
                    yield f"data: {json.dumps({'status': 'completed'})}\n\n"

            return StreamingResponse(session_id_stream(), media_type="text/event-stream")
        else:
            reply = await generate_chat_reply(history, chat_request.message, file_ids)
            await store_chat_message(db, thread_id, "assistant", reply)
            history = await get_chat_history(db, thread_id)

            logger.info(f"Trial chat message processed for session {session_id}, thread {thread_id}.")
            return success_response(
                200,
                "trial chat message processed",
                TrialChatResponse(
                    thread_id=thread_id,
                    title=thread_title,
                    history=history,
                    reply=reply,
                    trials_remaining=new_trials_remaining,
                    session_id=session_id  # Always return the session_id to the frontend
                ),
            )
    except HTTPException as e:
        logger.error("HTTPException in trial chat: %s", str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error("Error in trial chat: %s\nTraceback: %s", str(e), error_traceback)
        return fail_response(500, f"Error processing trial chat message: {str(e)}")


class TransferTrialRequest(BaseModel):
    session_id: str

@router.post("/transfer-trial-chats")
async def transfer_trial_chats(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    transfer_request: TransferTrialRequest = None,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Transfer trial chat threads to a registered user account.
    This endpoint should be called after a user registers and logs in.

    The frontend should store the session_id from trial chat responses
    and pass it to this endpoint after user registration and login.
    """
    try:
        if not transfer_request or not transfer_request.session_id:
            return fail_response(400, "session_id is required")

        session_id = transfer_request.session_id

        user_id = token["decoded"].get("user_id")
        logger.info(f"Transferring trial chats for session {session_id} to user {user_id}")

        # Find all threads associated with the trial session
        trial_user_id = f"trial_{session_id}"
        trial_org_id = f"trial_{session_id}"

        # Query for all trial threads
        thread_stmt = select(ChatThread).where(
            ChatThread.user_id == trial_user_id,
            ChatThread.organization_id == trial_org_id
        )
        result = await db.execute(thread_stmt)
        trial_threads = result.scalars().all()

        if not trial_threads:
            return success_response(200, "No trial chats found to transfer", {"transferred": 0})

        # Update each thread to associate with the registered user
        transferred_count = 0
        for thread in trial_threads:
            thread.user_id = user_id
            thread.organization_id = organization_id
            thread.updated_at = datetime.now()
            transferred_count += 1

        await db.commit()

        # Clear the trial count and personalization data from Redis
        try:
            trial_count_key = f"trial_count:{session_id}"
            personalization_key = f"trial_personalization:{session_id}"
            logger.info(f"Clearing trial data from Redis for session {session_id}")
            await redis_client.delete(trial_count_key)
            await redis_client.delete(personalization_key)
            logger.info(f"Successfully cleared trial data from Redis for session {session_id}")
        except Exception as redis_error:
            logger.error(f"Error clearing trial data from Redis: {str(redis_error)}")
            # Continue even if Redis delete fails

        # No need to delete the threads or messages since they've been transferred
        # But we should clean up any other temporary data associated with the trial session
        # For now, there's no additional data to clean up, but this is where we would do it
        # if we added any in the future (e.g., temporary user profiles, etc.)
        logger.info(f"No additional cleanup needed for session {session_id}")

        logger.info(f"Successfully transferred {transferred_count} trial chats to user {user_id}")
        return success_response(
            200,
            f"Successfully transferred {transferred_count} trial chats",
            {"transferred": transferred_count}
        )
    except HTTPException as e:
        logger.error("HTTPException in transfer trial chats: %s", str(e))
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("Error transferring trial chats: %s", str(e))
        return fail_response(500, "Error transferring trial chats")


@router.post("/upload-file", response_model=FileUploadResponse)
async def upload_file_for_chat(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db_session),
):
    """
    Upload a file to be used in chat.
    The file is uploaded to Gemini and the file ID is returned.
    """
    try:
        user_id = token["decoded"].get("user_id")
        logger.info(f"User {user_id} is uploading a file for chat: {file.filename}")

        # Check if file is empty
        if not file.filename:
            logger.error("Empty filename provided")
            return fail_response(400, "Empty filename provided")

        # Validate file size before reading (10MB limit for Gemini)
        # FastAPI handles this automatically via the 413 error, but we'll add an explicit check
        MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes

        try:
            # Get file size if available
            if hasattr(file, "size") and file.size > MAX_FILE_SIZE:
                logger.error(f"File too large: {file.size} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Read the file content
            file_content = await file.read()

            # Check size after reading if size wasn't available before
            if len(file_content) > MAX_FILE_SIZE:
                logger.error(f"File too large: {len(file_content)} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Check if file is empty
            if len(file_content) == 0:
                logger.error("Empty file uploaded")
                return fail_response(400, "Empty file uploaded")
        except Exception as e:
            logger.error(f"Error reading file: {str(e)}")
            return fail_response(500, f"Error reading file: {str(e)}")

        # Get the file type
        file_type = file.content_type or mimetypes.guess_type(file.filename)[0] or "application/octet-stream"

        # Special handling for markdown files
        if file.filename.endswith('.md'):
            file_type = "text/markdown"

        # Upload the file to Gemini with MIME type validation
        try:
            uploaded_file = await upload_file_to_gemini(file_content, file.filename, file_type)

            if not uploaded_file:
                logger.error("Failed to upload file to Gemini - null response")
                raise HTTPException(status_code=500, detail="Failed to upload file to Gemini")
        except HTTPException as e:
            # Re-raise HTTP exceptions with the same status code and detail
            logger.error(f"HTTP error during file upload to Gemini: {e.status_code} - {e.detail}")
            raise e
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error uploading file to Gemini: {error_msg}")

            # Check for specific error messages that might indicate a 413 error
            if "413" in error_msg or "too large" in error_msg.lower() or "entity too large" in error_msg.lower():
                return fail_response(413, "File too large for processing")

            # Check for network-related errors
            if "timeout" in error_msg.lower() or "connection" in error_msg.lower():
                return fail_response(503, "Service temporarily unavailable. Please try again later.")

            # Generic error
            return fail_response(500, f"Error uploading file to Gemini: {error_msg}")

        try:
            # Calculate expiry date (48 hours from now)
            expiry_date = datetime.now() + timedelta(hours=48)

            # Store file information in the database
            gemini_file = GeminiFile(
                id=uploaded_file.name,
                filename=file.filename,
                filetype=file_type,
                user_id=user_id,
                organisation_id=organization_id,
                upload_date=datetime.now(),
                expiry_date=expiry_date
            )

            db.add(gemini_file)
            await db.commit()

            logger.info(f"File uploaded to Gemini with ID: {uploaded_file.name}")

            return success_response(
                200,
                "File uploaded successfully",
                FileUploadResponse(
                    file_id=uploaded_file.name,
                    file_name=file.filename
                )
            )
        except Exception as db_error:
            logger.error(f"Database error after successful file upload: {str(db_error)}")
            # Even though DB operation failed, the file was uploaded to Gemini
            # We should return a partial success to the client with the file ID
            return success_response(
                207, # Partial success
                "File uploaded but metadata storage failed. Some features may be limited.",
                FileUploadResponse(
                    file_id=uploaded_file.name,
                    file_name=file.filename
                )
            )
    except HTTPException as e:
        logger.error(f"HTTPException in upload_file_for_chat: {e.status_code} - {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"Unexpected error uploading file for chat: {str(e)}\nTraceback: {error_traceback}")
        return fail_response(500, f"Error uploading file: {str(e)}")


@router.post("/trial-upload-file", response_model=FileUploadResponse)
async def upload_file_for_trial_chat(
    file: UploadFile = File(...),
    session_id: str = None,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Upload a file to be used in trial chat without authentication.
    The file is uploaded to Gemini and the file ID is returned.
    """
    try:
        # Validate session ID
        if not session_id:
            session_id = str(uuid.uuid4())
            logger.info(f"Generated new session ID for trial file upload: {session_id}")
        else:
            logger.info(f"Using provided session ID for trial file upload: {session_id}")

        # Check if file is empty
        if not file.filename:
            logger.error("Empty filename provided for trial upload")
            return fail_response(400, "Empty filename provided")

        # Check if the user has trials remaining
        try:
            trials_remaining = await get_trials_remaining(session_id)
            logger.info(f"Trials remaining for session {session_id}: {trials_remaining}")

            if trials_remaining <= 0:
                return fail_response(
                    403,
                    "You've used all your trial messages. Please register and log in to continue chatting.",
                    {"trials_remaining": 0, "session_id": session_id}
                )
        except Exception as e:
            logger.error(f"Error checking trial count: {e}")
            # If there's an error, allow the trial to continue with a default count
            trials_remaining = 5  # Default value

        # Validate file size before reading (10MB limit for Gemini)
        MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes

        try:
            # Get file size if available
            if hasattr(file, "size") and file.size > MAX_FILE_SIZE:
                logger.error(f"Trial upload - File too large: {file.size} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Read the file content
            file_content = await file.read()

            # Check size after reading if size wasn't available before
            if len(file_content) > MAX_FILE_SIZE:
                logger.error(f"Trial upload - File too large: {len(file_content)} bytes (max {MAX_FILE_SIZE} bytes)")
                return fail_response(413, f"File too large. Maximum file size is {MAX_FILE_SIZE // (1024 * 1024)}MB")

            # Check if file is empty
            if len(file_content) == 0:
                logger.error("Empty file uploaded for trial")
                return fail_response(400, "Empty file uploaded")
        except Exception as e:
            logger.error(f"Error reading file for trial upload: {str(e)}")
            return fail_response(500, f"Error reading file: {str(e)}")

        # Get the file type
        file_type = file.content_type or mimetypes.guess_type(file.filename)[0] or "application/octet-stream"

        # Special handling for markdown files
        if file.filename.endswith('.md'):
            file_type = "text/markdown"

        # Upload the file to Gemini with MIME type validation
        try:
            uploaded_file = await upload_file_to_gemini(file_content, file.filename, file_type)

            if not uploaded_file:
                logger.error("Failed to upload trial file to Gemini - null response")
                raise HTTPException(status_code=500, detail="Failed to upload file to Gemini")
        except HTTPException as e:
            # Re-raise HTTP exceptions with the same status code and detail
            logger.error(f"HTTP error during trial file upload to Gemini: {e.status_code} - {e.detail}")
            raise e
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error uploading trial file to Gemini: {error_msg}")

            # Check for specific error messages that might indicate a 413 error
            if "413" in error_msg or "too large" in error_msg.lower() or "entity too large" in error_msg.lower():
                return fail_response(413, "File too large for processing")

            # Check for network-related errors
            if "timeout" in error_msg.lower() or "connection" in error_msg.lower():
                return fail_response(503, "Service temporarily unavailable. Please try again later.")

            # Generic error
            return fail_response(500, f"Error uploading file to Gemini: {error_msg}")

        try:
            # Calculate expiry date (48 hours from now)
            expiry_date = datetime.now() + timedelta(hours=48)

            # Store file information in the database
            gemini_file = GeminiFile(
                id=uploaded_file.name,
                filename=file.filename,
                filetype=file_type,
                user_id=f"trial_{session_id}",
                organisation_id=f"trial_{session_id}",
                upload_date=datetime.now(),
                expiry_date=expiry_date
            )

            db.add(gemini_file)
            await db.commit()

            logger.info(f"File uploaded to Gemini with ID: {uploaded_file.name} for trial session {session_id}")

            return success_response(
                200,
                "File uploaded successfully",
                {
                    "file_id": uploaded_file.name,
                    "file_name": file.filename,
                    "session_id": session_id,
                    "trials_remaining": trials_remaining
                }
            )
        except Exception as db_error:
            logger.error(f"Database error after successful trial file upload: {str(db_error)}")
            # Even though DB operation failed, the file was uploaded to Gemini
            # We should return a partial success to the client with the file ID
            return success_response(
                207, # Partial success
                "File uploaded but metadata storage failed. Some features may be limited.",
                {
                    "file_id": uploaded_file.name,
                    "file_name": file.filename,
                    "session_id": session_id,
                    "trials_remaining": trials_remaining
                }
            )
    except HTTPException as e:
        logger.error(f"HTTPException in upload_file_for_trial_chat: {e.status_code} - {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"Unexpected error uploading file for trial chat: {str(e)}\nTraceback: {error_traceback}")
        return fail_response(500, f"Error uploading file: {str(e)}")


@router.get("/file")
async def get_file_info(
    file_id: str,
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session),
):
    """
    Get information about a file.
    """
    try:
        user_id = token["decoded"].get("user_id")
        logger.info(f"User {user_id} is getting file info for file: {file_id}")

        # Query the database for file information
        file_stmt = select(GeminiFile).where(
            GeminiFile.id == file_id,
            GeminiFile.organisation_id == organization_id
        )
        result = await db.execute(file_stmt)
        file_data = result.scalars().first()

        if not file_data:
            # Check if it's a trial file
            trial_file_stmt = select(GeminiFile).where(
                GeminiFile.id == file_id,
                GeminiFile.user_id.like(f"trial_%")
            )
            result = await db.execute(trial_file_stmt)
            file_data = result.scalars().first()

            if not file_data:
                raise HTTPException(status_code=404, detail="File not found")

        # Get the file from Gemini
        try:
            gemini_file = gemini_client.files.get(name=file_id)
            if not gemini_file:
                raise HTTPException(status_code=404, detail="File not found in Gemini")

            logger.info(f"Retrieved file from Gemini: {file_id}")

            # Return file information
            return success_response(
                200,
                "File information retrieved",
                {
                    "file_id": file_data.id,
                    "file_name": file_data.filename,
                    "file_type": file_data.filetype,
                    "upload_date": file_data.upload_date,
                    "expiry_date": file_data.expiry_date
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving file from Gemini: {e}")
            raise HTTPException(status_code=500, detail=f"Error retrieving file from Gemini: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException in get_file_info: {e}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Error getting file info: {e}")
        return fail_response(500, f"Error getting file info: {str(e)}")


@router.get("/trial-file")
async def get_trial_file_info(
    file_id: str,
    session_id: str,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Get information about a trial file without authentication.
    """
    try:
        logger.info(f"Getting trial file info for file: {file_id} with session ID: {session_id}")

        # Query the database for file information
        file_stmt = select(GeminiFile).where(
            GeminiFile.id == file_id,
            GeminiFile.user_id == f"trial_{session_id}"
        )
        result = await db.execute(file_stmt)
        file_data = result.scalars().first()

        if not file_data:
            raise HTTPException(status_code=404, detail="File not found")

        # Get the file from Gemini
        try:
            gemini_file = gemini_client.files.get(name=file_id)
            if not gemini_file:
                raise HTTPException(status_code=404, detail="File not found in Gemini")

            logger.info(f"Retrieved trial file from Gemini: {file_id}")

            # Return file information
            return success_response(
                200,
                "File information retrieved",
                {
                    "file_id": file_data.id,
                    "file_name": file_data.filename,
                    "file_type": file_data.filetype,
                    "upload_date": file_data.upload_date,
                    "expiry_date": file_data.expiry_date,
                    "session_id": session_id
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving trial file from Gemini: {e}")
            raise HTTPException(status_code=500, detail=f"Error retrieving file from Gemini: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException in get_trial_file_info: {e}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"Error getting trial file info: {e}")
        return fail_response(500, f"Error getting file info: {str(e)}")
